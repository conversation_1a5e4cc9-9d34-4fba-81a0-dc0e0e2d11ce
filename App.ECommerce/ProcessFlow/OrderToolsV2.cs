using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.Transport;
using App.ECommerce.Units;
using App.ECommerce.Units.Enums;

using AutoMapper;

using log4net;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;

using MongoDB.Bson;

using Newtonsoft.Json;

using System.Net;

namespace App.ECommerce.ProcessFlow;

public class OrderToolsV2(
    IUserRepository _userRepository,
    IPartnerRepository _partnerRepository,
    ICartRepository _cartRepository,
    IShippingAddressRepository _shippingAddressRepository,
    IShopRepository _shopRepository,
    IItemsRepository _itemsRepository,
    IVoucherRepository _voucherRepository,
    ITagRepository _tagRepository,
    IStringLocalizer _stringLocalizer,
    IItemOptionRepository _itemOptionRepository,
    ITransportService _transportService,
    ISettingRepository _settingRepository,
    ICalcMemberLevelFlow _calcMemberLevelFlow,
    ITransportMethodRepository _transportMethodRepository,
    OrderTools _orderTools,
    IMapper _mapper,
    IPriceListFlow _priceListFlow
    ) : IOrderToolsV2
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(OrderToolsV2));

    public async Task<(string? errors, CartDto? cartDto)> CreateOrUpdateCart(
        string requestId,
        CartDto model,
        HttpContext httpContext,
        bool isForUser = false,
        string partnerId = "",
        string userId = "")
    {
        var cart = _mapper.Map<Cart>(model);
        if (!isForUser)
        {
            if (string.IsNullOrEmpty(partnerId)) return ("BASE_USER_AUTH_NOT_FOUND", null);
            var partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ("BASE_USER_AUTH_NOT_FOUND", null);
            if (partner.PartnerId != model.PartnerId) return ("BASE_USER_AUTH_NOT_FOUND", null);
        }
        else
        {
            var cartOfUser = await _cartRepository.GetCurrentCartOfUser(userId);
            if (cartOfUser != null)
            {
                cart = cartOfUser;
                cart.TypePay = model.TypePay;
                cart.VoucherPromotion = model.VoucherPromotion;
                cart.VoucherTransport = model.VoucherTransport;
                cart.VoucherPromotionIds = model.VoucherPromotion
                    .Where(v => v.VoucherId != null)
                    .Select(v => v.VoucherId)
                    .ToList();
                cart.VoucherTransportIds = model.VoucherTransport
                    .Where(v => v.VoucherId != null)
                    .Select(v => v.VoucherId)
                    .ToList();
                cart.AddressId = model.AddressId;
                cart.StatusDelivery = model.StatusDelivery;
                cart.BranchId = model.BranchId;
                cart.OriginPrice = (model.ListItems.Sum(item => item.Quantity * item.Price) ?? 0);
                cart.CartOrigin = model.CartOrigin;
                cart.VoucherCodes = model.VoucherCodes ?? new List<string>();
                cart.Notes = model.Notes;
            }
            //Cập nhật TransportService

        }

        //==== Cập nhật giá, số lượng
        cart.ListItems = CalculatorItemsOrder(model.ListItems);
        if (cart.StatusDelivery == TypeDelivery.InShop)
        {
            cart.TransportService = null;
            // Khi chuyển sang InShop, cần loại bỏ các voucher transport khỏi voucherCodes
            await RemoveInvalidTransportVouchersFromCodes(cart);
        }
        else
        {
            cart.TransportService = GetDefaultTransportService(cart);
        }
        var user = _userRepository.FindByUserId((isForUser ? userId : model.UserId) ?? "");
        if (user == null)
        {
            if ((cart.VoucherPromotion != null && cart.VoucherPromotion.Any()) ||
                (cart.VoucherTransport != null && cart.VoucherTransport.Any()))
                return ("CART_USER_NOT_FOUND", null);

            if (model.ExchangePoints > 0.0)
                model.ExchangePoints = 0;
        }

        if (!string.IsNullOrEmpty(cart.CartId))
        {
            var cartCheck = _cartRepository.FindByCartId(cart.CartId);
            if (cartCheck == null)
                return ("CART_NOT_FOUND", null);
        }

        if (!string.IsNullOrEmpty(cart.AddressId))
        {
            var address = _shippingAddressRepository.FindByShippingAddressId(cart.AddressId);
            if (address == null)
                return ("ADDRESS_NOT_FOUND", null);
        }

        var shop = _shopRepository.FindByShopId(isForUser ? (user?.ShopId ?? "") : cart.ShopId);
        if (shop == null)
            return ("SHOP_NOT_FOUND", null);

        if (!isForUser)
        {
            if (shop.PartnerId != partnerId)
                return ("SHOP_NOT_FOUND", null);
        }

        var checkItemsIds = cart.ListItems?.Select(x => x.ItemsId).Distinct().ToList() ?? new List<string>();
        var checkItemsList = _itemsRepository.FindByItemsIds(string.Join(",", checkItemsIds));
        if (checkItemsIds.Count != checkItemsList.Count)
            return ("CART_LIST_ITEMS_INVALID", null);

        foreach (var items in cart.ListItems ?? new List<ItemsOrder>())
        {
            Items? checkItems = checkItemsList.FirstOrDefault(x => x.ItemsId == items.ItemsId);

            if (checkItems == null)
                return ("CART_LIST_ITEMS_INVALID", null);
            if (checkItems.Quantity <= 0)
                return ("CART_LIST_ITEMS_QUANTITY_PURCHASE_INVALID", null);

            Items item = _mapper.Map<Items>(checkItems);

            Items resultDto = await _priceListFlow.CalculatePriceItemWithPriceList(item, userId);

            items.ItemsCode = checkItems.ItemsCode;
            items.PartnerId = checkItems.PartnerId;
            items.ShopId = checkItems.ShopId;
            items.ItemsType = checkItems.ItemsType;
            items.CategoryIds = checkItems.CategoryIds;
            items.ItemsName = checkItems.ItemsName;
            items.ItemsNameOrigin = checkItems.ItemsNameOrigin;
            items.ItemsInfo = checkItems.ItemsInfo;
            items.Images = checkItems.Images;
            items.Sold = checkItems.Sold;
            items.IsVariant = checkItems.IsVariant;
            items.VariantImage = checkItems.VariantImage;
            items.VariantNameOne = checkItems.VariantNameOne;
            items.VariantValueOne = checkItems.VariantValueOne;
            items.VariantNameTwo = checkItems.VariantNameTwo;
            items.VariantValueTwo = checkItems.VariantValueTwo;
            items.VariantNameThree = checkItems.VariantNameThree;
            items.VariantValueThree = checkItems.VariantValueThree;
            items.PriceCapital = checkItems.PriceCapital ?? 0; // Giá vốn của sản phẩm
            items.PriceReal = checkItems.PriceReal ?? 0; // Giá gốc của sản phẩm
            items.Price = resultDto.Price ?? 0; //Giá thực tế sản phẩm
            //items.Quantity = checkItems.Quantity ?? 0; //Số lượng tồn kho của sản phẩm or số lượng mua của đơn hàng
            items.QuantityPurchase = checkItems.QuantityPurchase ?? 0; //Số lượng đã bán của sản phẩm
            items.SellOver = checkItems.SellOver ?? false; //Bán vượt số lượng quy định
            items.WarehouseId = checkItems.WarehouseId;
            items.ItemsWeight = checkItems.ItemsWeight;
            items.ItemsLength = checkItems.ItemsLength;
            items.ItemsHeight = checkItems.ItemsHeight;

            if (checkItems.CustomTaxRate != null)
            {
                // Lấy thuế từ sản phẩm
                var taxRate = (long)(checkItems?.CustomTaxRate ?? 0);
                if (taxRate != null)
                {
                    items.TaxRate = taxRate;
                    items.TaxAmount = Math.Round((items.Price ?? 0) * (items.Quantity ?? 0) * ((items?.TaxRate ?? 0) / 100m), 0, MidpointRounding.AwayFromZero);
                }
            }
            else
            {
                // Nếu không có thuế tùy chỉnh, sử dụng thuế mặc định của shop
                items.TaxRate = (long)(shop?.DefaultTaxRate ?? 0);
                items.TaxAmount = Math.Round((items.Price ?? 0) * (items.Quantity ?? 0) * ((items?.TaxRate ?? 0) / 100m), 0, MidpointRounding.AwayFromZero);
            }
        }

        // Validate VoucherPromotion
        var checkVoucherIds = cart.VoucherPromotion?.Select(x => x.VoucherId)?.ToList() ?? new List<string>();
        var checkVoucherList = await _voucherRepository.FindByVoucherIds(string.Join(",", checkVoucherIds));
        if (checkVoucherIds.Count != checkVoucherList.Count)
            return ("CART_LIST_VOUCHER_PROMOTION_INVALID", null);
        // Validate VoucherTransport
        checkVoucherIds = cart.VoucherTransport?.Select(x => x.VoucherId)?.ToList() ?? new List<string>();
        checkVoucherList = await _voucherRepository.FindByVoucherIds(string.Join(",", checkVoucherIds));
        if (checkVoucherIds.Count != checkVoucherList.Count)
            return ("CART_LIST_VOUCHER_TRANSPORT_INVALID", null);
        // Validate Voucher still available
        double userPoint = user?.Point ?? 0.0;
        double minOrder = cart.ListItems.Sum(x => x.Price ?? 0.0);
        List<string> categoryIds = cart.ListItems.Select(x => x.CategoryIds).SelectMany(c => c).Distinct().ToList();
        List<string> itemsIds = cart.ListItems.Select(x => x.ItemsId).Distinct().ToList();
        List<string> groupNames = _tagRepository.GetTagsForUser(user?.UserId);
        List<string> userIds = new List<string>() { user?.UserId };

        if (cart.ListItems.Count > 0)
        {
            var itemOptionIds = model.ListItems.SelectMany(i => i.ExtraOptions).ToList();
            var itemOptions = await _itemOptionRepository.GetItemOptionsByIds(itemOptionIds);

            OrderTools.ValidateOrderModel validateOrder = await _orderTools.ValidateAndCalculatorOrder(
                requestId,
                isSaveOrder: false,
                exchangePoints: model.ExchangePoints ?? 0,
                user: user ?? new User(),
                addressId: cart.AddressId,
                cart: cart,
                _stringLocalizer,
                _shopRepository,
                _itemsRepository,
                _voucherRepository,
                _tagRepository,
                _transportService,
                _calcMemberLevelFlow,
                _shippingAddressRepository,
                httpContext,
                isForUser,
                itemOptions.ToList()
            );

            if (validateOrder.Status == HttpStatusCode.BadRequest)
                return (validateOrder.Message, null);

            OrderTools.ResultCalculator? objCaculator = validateOrder.Data;

            if (objCaculator != null)
            {
                // Validate các giá trị giảm giá
                if (objCaculator.TotalVoucherPromotionPrice < 0 ||
                    objCaculator.TotalVoucherTransportPrice < 0 ||
                    objCaculator.TotalPoint < 0)
                {
                    return ("INVALID_DISCOUNT_VALUES", null);
                }

                //==== Cập nhật giá
                cart.Price = objCaculator.TotalPriceOrder; //Tổng giá trị đơn hàng
                cart.VoucherPromotionPrice = objCaculator.TotalVoucherPromotionPrice; //Tổng tiền voucher giảm giá
                cart.VoucherTransportPrice = objCaculator.TotalVoucherTransportPrice; //Tổng tiền voucher vận chuyển
                cart.TransportPrice = objCaculator.TotalTransportPrice; //Tổng phí vận chuyển    
                cart.PointPrice = objCaculator.TotalPointPrice; //Tổng tiền giảm giá bằng đổi điểm (đã được tính toán lại sau khi áp dụng voucher)
                cart.ExchangePoints = objCaculator.TotalPoint; //Tổng điểm đổi ra tiền (đã được reset nếu có voucher)
                cart.TotalTaxAmount = Math.Round(objCaculator.TotalTaxAmount, 0, MidpointRounding.AwayFromZero); // Tổng tiền thuế
                cart.TotalAfterTax = Math.Round(objCaculator.TotalAfterTax + Math.Max(0, objCaculator.TotalTransportPrice - cart.VoucherTransportPrice), 0, MidpointRounding.AwayFromZero); // Tổng tiền sau thuế

                cart.BranchId = objCaculator?.ListOrder[0]?.BranchId;

                _log.Info($"cart.TotalAfterTax: {cart.TotalAfterTax}");
                _log.Info($"cart.VoucherPromotionPrice: {cart.VoucherPromotionPrice}");
                _log.Info($"cart.VoucherTransportPrice: {cart.VoucherTransportPrice}");
                _log.Info($"objCaculator.TotalTransportPrice: {objCaculator.TotalTransportPrice}");

                // Kiểm tra TotalAfterTax không được âm
                if (cart.TotalAfterTax < 0)
                    return ("TOTAL_AFTER_TAX_CANNOT_BE_NEGATIVE", null);
            }
            else
            {
                cart.Price = 0; //Tổng giá trị đơn hàng
                cart.VoucherPromotionPrice = 0; //Tổng tiền voucher giảm giá
                cart.VoucherTransportPrice = 0; //Tổng tiền voucher vận chuyển
                cart.TransportPrice = 0; //Tổng phí vận chuyển    
                cart.PointPrice = 0;
                cart.ExchangePoints = 0;
                cart.TotalTaxAmount = 0;
                cart.TotalAfterTax = 0;
            }
        }
        else
        {
            cart.Price = 0; //Tổng giá trị đơn hàng
            cart.VoucherPromotionPrice = 0; //Tổng tiền voucher giảm giá
            cart.VoucherTransportPrice = 0; //Tổng tiền voucher vận chuyển
            cart.TransportPrice = 0; //Tổng phí vận chuyển  
            cart.PointPrice = 0;
            cart.ExchangePoints = 0;
            cart.TotalTaxAmount = 0;
            cart.TotalAfterTax = 0;
        }
        var cartUpdate = _cartRepository.FindByCartId(cart.CartId);
        if (cartUpdate == null)
        {
            cartUpdate = _mapper.Map<Cart>(cart);
            cartUpdate.CartId = "";
            cartUpdate.OriginPrice = (cart.ListItems.Sum(x => x.Price * x.Quantity) ?? 0);
            cartUpdate.TransactionId = ""; //Guid.NewGuid().ToString()
            cartUpdate.PartnerId = partnerId;
            cartUpdate.UserId = isForUser ? userId : cart.UserId;
            cartUpdate.AddressId = cart.AddressId;
            cartUpdate.ShopId = shop.ShopId;
            cartUpdate.ListItems = cart.ListItems ?? new List<ItemsOrder>();
            //cart.ListOrder = cart.ListOrder;
            cartUpdate.VoucherPromotionIds =
                cart.VoucherPromotion?.Select(x => x.VoucherId)?.ToList() ?? new List<string>();
            cartUpdate.VoucherPromotion = cart.VoucherPromotion;
            cartUpdate.VoucherTransportIds =
                cart.VoucherTransport?.Select(x => x.VoucherId)?.ToList() ?? new List<string>();
            cartUpdate.VoucherTransport = cart.VoucherTransport;
            cartUpdate.Price = cart.Price;
            cartUpdate.VoucherPromotionPrice = cart.VoucherPromotionPrice;
            cartUpdate.VoucherTransportPrice = cart.VoucherTransportPrice;
            cartUpdate.TransportPrice = cart.TransportPrice;
            cartUpdate.TransportService = cart.TransportService; // [ LCOD, NCOD, VHT...]
            cartUpdate.TypePay = cart.TypePay; // [COD, Momo, Zalo, Other...]
            cartUpdate.UserId = cart.UserId;
            cartUpdate.BranchId = cart.BranchId;
            cartUpdate.CartOrigin = cart.CartOrigin;
            cartUpdate.StatusDelivery = cart.StatusDelivery;
            cartUpdate.Status = TypeStatus.Actived;
            cartUpdate.Created = DateTimes.Now();
            cartUpdate.Updated = DateTimes.Now();
            cartUpdate.SavingMoney = cart.VoucherPromotionPrice + cart.VoucherTransportPrice + cart.PointPrice;
            cartUpdate.PointPrice = cart.PointPrice;
            cartUpdate.ExchangePoints = cart.ExchangePoints;
            cartUpdate.PaymentId = cart.PaymentId;
            cartUpdate.TaxInvoice = model.TaxInvoice;
            cartUpdate.TotalTaxAmount = cart.TotalTaxAmount;
            cartUpdate.TotalAfterTax = cart.TotalAfterTax; // Không cần trừ SavingMoney nữa vì đã được tính trong công thức đầu
            cartUpdate.VoucherCodes = cart.VoucherCodes;
            cartUpdate.Notes = cart.Notes;
            cartUpdate = await _cartRepository.CreateCart(cartUpdate);
        }
        else
        {
            //cart.CartId = cart.CartId;
            //cart.TransactionId = cart.TransactionId;
            //cart.PartnerId = partner.PartnerId;
            cartUpdate.UserId = cart.UserId;
            cartUpdate.AddressId = cart.AddressId;
            cartUpdate.ShopId = shop.ShopId;
            cartUpdate.ListItems = cart.ListItems ?? new List<ItemsOrder>();
            //cart.ListOrder = cart.ListOrder;
            cartUpdate.VoucherPromotionIds =
                cart.VoucherPromotion?.Select(x => x.VoucherId)?.ToList() ?? new List<string>();
            cartUpdate.VoucherPromotion = cart.VoucherPromotion;
            cartUpdate.VoucherTransportIds =
                cart.VoucherTransport?.Select(x => x.VoucherId)?.ToList() ?? new List<string>();
            cartUpdate.VoucherTransport = cart.VoucherTransport;
            cartUpdate.Price = cart.Price;
            cartUpdate.OriginPrice = (cart.ListItems.Sum(x => x.Price * x.Quantity) ?? 0);
            cartUpdate.VoucherPromotionPrice = cart.VoucherPromotionPrice;
            cartUpdate.VoucherTransportPrice = cart.VoucherTransportPrice;
            cartUpdate.TransportPrice = cart.TransportPrice;
            cartUpdate.TransportService = cart.TransportService; // [ LCOD, NCOD, VHT...]
            cartUpdate.TypePay = cart.TypePay; // [COD, Momo, Zalo, Other...]
            cartUpdate.UserId = cart.UserId;
            cartUpdate.StatusDelivery = cart.StatusDelivery;
            cartUpdate.Status = TypeStatus.Actived;
            cartUpdate.Created = DateTimes.Now();
            cartUpdate.Updated = DateTimes.Now();
            cartUpdate.BranchId = cart.BranchId;
            cartUpdate.SavingMoney = cart.VoucherPromotionPrice + cart.VoucherTransportPrice + cart.PointPrice;
            cartUpdate.CartOrigin = cart.CartOrigin;
            cartUpdate.PointPrice = cart.PointPrice;
            cartUpdate.ExchangePoints = cart.ExchangePoints;
            cartUpdate.PaymentId = cart.PaymentId;
            cartUpdate.TaxInvoice = model.TaxInvoice;
            cartUpdate.TotalTaxAmount = cart.TotalTaxAmount;
            cartUpdate.TotalAfterTax = cart.TotalAfterTax; // Không cần trừ SavingMoney nữa vì đã được tính trong công thức đầu
            cartUpdate.VoucherCodes = cart.VoucherCodes;
            cartUpdate.Notes = cart.Notes;
            cartUpdate = await _cartRepository.UpdateCart(cartUpdate);
        }

        var cartDto = _mapper.Map<CartDto>(cartUpdate);

        return ("", cartDto);
    }

    public TypeTransportService GetDefaultTransportService(Cart cart)
    {
        var shopId = cart.ShopId;
        var transportMethods = _transportMethodRepository.GetByShopId(shopId);

        // Kiểm tra xem có sản phẩm nào có TransportType là Standard
        bool hasStandardTransportItems = false;

        if (cart.ListItems?.Any() == true)
        {
            var itemIds = cart.ListItems.Select(x => x.ItemsId).Distinct().ToList();
            if (itemIds.Any())
            {
                var items = _itemsRepository.FindByItemsIds(string.Join(",", itemIds));

                // Kiểm tra nếu có bất kỳ sản phẩm nào yêu cầu vận chuyển Standard
                hasStandardTransportItems = items.Any(item =>
                    item.TransportType?.Contains(TransportServiceType.Standard) == true);
            }
        }

        // Xác định loại dịch vụ vận chuyển dựa trên kết quả kiểm tra
        var serviceType = hasStandardTransportItems
            ? TransportServiceType.Standard
            : TransportServiceType.Express;

        // Tìm transport method phù hợp với loại dịch vụ
        var matchedMethod = transportMethods
            .FirstOrDefault(tm =>
                tm.IsEnabled &&
                tm.ServiceOptions.Any(opt =>
                    opt.ServiceType == serviceType && opt.IsEnabled));

        if (matchedMethod == null || cart.AddressId == null)
            return TypeTransportService.LCOD;

        // Xác định loại transport service dựa trên transport method
        return matchedMethod.TransportCode switch
        {
            TransportCodeType.Ahamove => TypeTransportService.AHAMOVE,
            TransportCodeType.JTExpress => TypeTransportService.JTEXPRESS,
            _ => TypeTransportService.LCOD
        };
    }

    #region Private methods

    private List<ItemsOrder> CalculatorItemsOrder(List<ItemsOrder>? listItems)
    {
        if (listItems == null || listItems.Count == 0)
            return [];

        var groupedItems = new Dictionary<(string ItemsCode, string ExtraOptions), ItemsOrder>();
        foreach (var item in listItems)
        {
            item.Note = item.Note?.ToLower().Trim();
            var extraOptionsKey = string.Join(",", item.ExtraOptions.OrderBy(opt => opt));

            var key = (item.ItemsId, extraOptionsKey);
            if (!string.IsNullOrWhiteSpace(item.Note))
                key = (item.ItemsId, extraOptionsKey + item.Note);

            if (groupedItems.TryGetValue(key, out var groupedItem))
                groupedItem.Quantity += item.Quantity;
            else
                groupedItems[key] = item;
        }

        return groupedItems.Values.ToList();
    }

    /// <summary>
    /// Loại bỏ các voucher transport không hợp lệ khỏi voucherCodes khi statusDelivery thay đổi
    /// </summary>
    /// <param name="cart">Cart cần kiểm tra</param>
    private async Task RemoveInvalidTransportVouchersFromCodes(Cart cart)
    {
        if (cart.VoucherCodes == null || !cart.VoucherCodes.Any())
            return;

        var validVoucherCodes = new List<string>();
        var cartDto = _mapper.Map<CartDto>(cart);

        foreach (var voucherCode in cart.VoucherCodes)
        {
            try
            {
                // Lấy thông tin voucher từ code
                var voucher = await _voucherRepository.GetVoucherByCode(voucherCode, cart.UserId ?? "");
                if (voucher == null)
                {
                    // Nếu không tìm thấy voucher, bỏ qua code này
                    continue;
                }

                // Kiểm tra nếu là voucher transport và statusDelivery = InShop thì loại bỏ
                if (voucher.VoucherType == TypeVoucher.Transport &&
                    cart.StatusDelivery == TypeDelivery.InShop)
                {
                    // Không thêm voucher transport vào danh sách hợp lệ khi InShop
                    continue;
                }

                // Kiểm tra voucher có còn hợp lệ với cart hiện tại không
                bool isValid = voucher.VoucherType == TypeVoucher.Transport
                    ? ValidateVoucher.IsTransportVoucherEnabled(voucher, cartDto)
                    : ValidateVoucher.IsPromotionVoucherEnabled(voucher, cartDto);

                if (isValid)
                {
                    validVoucherCodes.Add(voucherCode);
                }
            }
            catch (Exception ex)
            {
                // Log lỗi nhưng tiếp tục xử lý các voucher khác
                _log.Warn($"Error validating voucher code {voucherCode}: {ex.Message}");
            }
        }

        // Cập nhật lại danh sách voucherCodes chỉ với các voucher hợp lệ
        cart.VoucherCodes = validVoucherCodes;
    }

    #endregion

}

public interface IOrderToolsV2
{
    Task<(string? errors, CartDto? cartDto)> CreateOrUpdateCart(string requestId, CartDto model, HttpContext httpContext,
        bool isForUser, string partnerId, string userId);

    TypeTransportService GetDefaultTransportService(Cart cart);
}
