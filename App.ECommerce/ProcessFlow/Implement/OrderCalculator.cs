using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.ProcessFlow.Models;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Services.Transport;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;

using AutoMapper;

using log4net;

using Newtonsoft.Json;

namespace App.ECommerce.ProcessFlow.Implement;

public class OrderCalculator : IOrderCalculator
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(OrderCalculator));
    private readonly ITransportService _transportService;
    private readonly ICalcMemberLevelFlow _calcMemberLevelFlow;
    private readonly IShopRepository _shopRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly IVoucherFlow _voucherFlow;
    private readonly IMapper _mapper;
    private readonly IPriceListFlow _priceListFlow;

    public OrderCalculator(
        ITransportService transportService,
        ICalcMemberLevelFlow calcMemberLevelFlow,
        IShopRepository shopRepository,
        IItemsRepository itemsRepository,
        IVoucherFlow voucherFlow,
        IMapper mapper,
        IPriceListFlow priceListFlow)
    {
        _transportService = transportService;
        _calcMemberLevelFlow = calcMemberLevelFlow;
        _shopRepository = shopRepository;
        _itemsRepository = itemsRepository;
        _voucherFlow = voucherFlow;
        _mapper = mapper;
        _priceListFlow = priceListFlow;
    }

    public async Task<OrderCalculationModel> CalculateOrderPrice(Cart obj,
        List<Voucher> promotionVouchers,
        List<Voucher> transportVouchers,
        int exchangePoints)
    {
        var result = new OrderCalculationModel
        {
            TransactionId = Guid.NewGuid().ToString()
        };

        // Lấy thông tin shop và items để tính thuế
        var shop = _shopRepository.FindByShopId(obj.ListItems.FirstOrDefault()?.ShopId);
        var itemsList = _itemsRepository.FindByItemsIds(string.Join(",", obj.ListItems.Select(x => x.ItemsId)));

        // Tính tổng giá trị đơn hàng
        long totalAmount = await CalculateItemsTotalAsync(obj.ListItems, itemsList, obj.UserId);
        result.TotalBeforeTax = totalAmount;

        // Tính thuế VAT cho từng sản phẩm và tổng hợp
        decimal totalTaxAmount = 0;
        var taxSummaryDict = new Dictionary<TaxRateEnum, (decimal TaxRate, decimal Amount, decimal Subtotal)>();

        foreach (var item in obj.ListItems)
        {
            var detailItem = itemsList.FirstOrDefault(x => x.ItemsId == item.ItemsId);
            if (detailItem == null) continue;

            if (!item.Price.HasValue || !item.Quantity.HasValue) continue;

            var priceExtraOptionOfItem = item.ExtraOptionsDetail?
                  .SelectMany(group => group.ItemOptions ?? new List<ItemOption>())
                  .Where(option => item.ExtraOptions.Contains(option.ItemOptionId))
                  .Sum(option => option.Price) ?? 0; // Giá options * số lượng

            long itemTotalPrice = ((detailItem.Price ?? 0) + priceExtraOptionOfItem) * (item.Quantity ?? 0);
            double ratio = (double)itemTotalPrice / totalAmount;

            item.TotalBeforeTax = itemTotalPrice;

            // Phân bổ giảm giá voucher
            if (obj.VoucherPromotionPrice > 0 && obj.VoucherPromotion?.Count > 0)
                item.VoucherDiscount = (long)(obj.VoucherPromotionPrice * ratio);
            else
                item.VoucherDiscount = 0;

            // Phân bổ giảm giá điểm
            if (obj.ExchangePoints > 0 && obj.PointPrice > 0)
                item.PointDiscount = (long)(obj.PointPrice * ratio);
            else
                item.PointDiscount = 0;

            var taxRate = GetTaxRateForItem(shop, detailItem);
            item.TaxRate = taxRate;

            decimal totalDiscount = item.VoucherDiscount + item.PointDiscount;

            item.TotalBeforeTax -= totalDiscount;
            if (item.TotalBeforeTax < 0) item.TotalBeforeTax = 0;
            item.TaxAmount = (item.TotalBeforeTax * item.TaxRate) / 100;
            if (item.TaxAmount < 0) item.TaxAmount = 0;
            item.TotalAfterTax = item.TotalBeforeTax + item.TaxAmount;
            if (item.TotalAfterTax < 0) item.TotalAfterTax = 0;

            totalTaxAmount += item.TaxAmount;

            // Phân loại thuế theo TaxRateEnum
            var taxRateType = GetTaxRateType(taxRate);
            if (!taxSummaryDict.ContainsKey(taxRateType))
            {
                taxSummaryDict[taxRateType] = (0, 0, 0);
            }
            var current = taxSummaryDict[taxRateType];

            decimal amount = current.Amount + item.TaxAmount;
            decimal subtotal = current.Subtotal + item.TotalBeforeTax;

            taxSummaryDict[taxRateType] = (taxRate, amount, subtotal);

            _log.Info($"CalculateOrderPrice CartNo: {obj.CartNo} | ItemsCode: {item.ItemsCode} | Price: {detailItem.Price} | TaxRate: {item.TaxRate} | totalDiscount: {totalDiscount}");
            _log.Info($"CalculateOrderPrice CartNo: {obj.CartNo} | TotalBeforeTax: {item.TotalBeforeTax}  | TaxAmount: {item.TaxAmount} | TotalAfterTax: {item.TotalAfterTax} -------");
        }

        // Tạo TaxSummary từ dictionary đã phân loại
        result.TaxSummary = new List<TaxSummaryLine>();
        foreach (var taxGroup in taxSummaryDict)
        {
            result.TaxSummary.Add(new TaxSummaryLine
            {
                TaxRateType = taxGroup.Key,
                TaxRate = taxGroup.Value.TaxRate,
                TaxAmount = taxGroup.Value.Amount,
                Subtotal = taxGroup.Value.Subtotal
            });
        }

        result.TotalTaxAmount = Math.Round(totalTaxAmount, 0, MidpointRounding.AwayFromZero);

        // Áp dụng voucher khuyến mãi
        var promotionDiscount = _voucherFlow.CalculatePromotionDiscount(totalAmount, promotionVouchers);
        if (promotionDiscount > totalAmount) promotionDiscount = totalAmount;
        _log.Info($"Phí giảm giá: {promotionDiscount}");
        result.TotalPromotionDiscount = promotionDiscount;
        result.TotalBeforeTax -= promotionDiscount;
        if (result.TotalBeforeTax < 0) result.TotalBeforeTax = 0;

        // Áp dụng điểm thành viên
        if (obj.ExchangePoints > 0)
        {
            var pointDiscount = await CalculatePointDiscount(obj);
            result.TotalPointDiscount = pointDiscount;
            result.TotalPoints = (int)exchangePoints;
            result.TotalBeforeTax -= pointDiscount;
            if (result.TotalBeforeTax < 0) result.TotalBeforeTax = 0;
        }

        // Tính tổng sau thuế
        result.TotalAfterTax = Math.Round(result.TotalBeforeTax + result.TotalTaxAmount, 0, MidpointRounding.AwayFromZero);
        if (result.TotalAfterTax < 0) result.TotalAfterTax = 0;

        _log.Info($"CalculateOrderPrice TotalBeforeTax: {result.TotalBeforeTax}");
        _log.Info($"CalculateOrderPrice TotalTaxAmount: {result.TotalTaxAmount}");

        return await Task.FromResult(result);
    }

    public async Task<(OrderCalculationModel result, string? branchId)> CalculateShippingFee(
        Cart cart,
        ShippingAddress address,
        Shop shop,
        List<Voucher> transportVouchers)
    {
        var result = new OrderCalculationModel();
        string? branchIdWithLowestShippingFee = cart.BranchId;

        // Tính phí vận chuyển gốc
        decimal originalShippingFee = 0;
        if (cart.StatusDelivery == TypeDelivery.ExpressDelivery)
        {
            if (cart.TransportService == TypeTransportService.AHAMOVE ||
                cart.TransportService == TypeTransportService.JTEXPRESS)
            {
                // Phân biệt giữa user và partner
                if (string.IsNullOrEmpty(cart.PartnerId)) // User tạo đơn
                {
                    var (branch, transportPrice) = await _transportService.FindBranchWithLowestShippingFee(address, cart);
                    if (branch != null)
                    {
                        originalShippingFee = transportPrice;
                        branchIdWithLowestShippingFee = branch.BranchId;
                    }
                    else
                    {
                        cart.TransportService = TypeTransportService.LCOD;
                        originalShippingFee = shop.TransportPrice ?? Constants.TransportPrice;
                    }
                }
                else // Partner tạo đơn
                {
                    var transportEstimate = await _transportService.EstimateOrder(cart);
                    if (transportEstimate.Success)
                    {
                        originalShippingFee = transportEstimate.Data;
                        branchIdWithLowestShippingFee = cart.BranchId;
                    }
                    else
                    {
                        originalShippingFee = shop.TransportPrice ?? Constants.TransportPrice;
                    }
                }
            }
            else
            {
                originalShippingFee = shop.TransportPrice ?? Constants.TransportPrice;
            }
        }

        long totalPrice = 0;
        totalPrice = cart.ListItems.Sum(items =>
        {
            return ((items.Price ?? 0) * (items.Quantity ?? 0)); // (Giá thực tế giảm * số lượng) + giá options
        });

        // Tính giảm giá từ voucher
        decimal shippingDiscount = _voucherFlow.CalculateShippingDiscount(originalShippingFee, totalPrice, transportVouchers, cart.Price);

        // Cập nhật kết quả
        result.TotalShippingFee = originalShippingFee;
        result.TotalShippingDiscount = shippingDiscount;
        _log.Info($"TotalShippingFee : {result.TotalShippingFee}");
        _log.Info($"TotalShippingDiscount : {result.TotalShippingDiscount}");
        return (result, branchIdWithLowestShippingFee);
    }

    private async Task<long> CalculateItemsTotalAsync(List<ItemsOrder> items, List<Items> detailItems, string userId)
    {
        long total = 0;
        foreach (var x in items)
        {
            var detailItem = detailItems.FirstOrDefault(i => i.ItemsId == x.ItemsId);
            if (detailItem == null) continue;

            Items resultDto = await _priceListFlow.CalculatePriceItemWithPriceList(detailItem, userId);

            var priceExtraOptionOfItem = x.ExtraOptionsDetail?
                  .SelectMany(group => group.ItemOptions ?? new List<ItemOption>())
                  .Where(option => x.ExtraOptions.Contains(option.ItemOptionId))
                  .Sum(option => option.Price) ?? 0;

            total += ((resultDto.Price ?? 0) + priceExtraOptionOfItem) * (x.Quantity ?? 0);
        }
        return total;
    }

    private async Task<long> CalculatePointDiscount(Cart obj)
    {
        try
        {
            string message = "";
            int point = 0;
            long pointPrice = 0;

            long cartPrice = (obj.OriginPrice - obj.VoucherPromotionPrice);

            _log.Info($"CalculatePointDiscount cartPrice: {cartPrice} ");

            (message, point, pointPrice) = await _calcMemberLevelFlow.CalcMaxPointCanUse(cartPrice, obj.UserId);

            (string? error, int maxExchangePoint, long cartPointPrice) = await _calcMemberLevelFlow.CalcMaxPointCanUse(cartPrice, obj.UserId);

            _log.Info($"CalculatePointDiscount maxExchangePoint: {maxExchangePoint} ");
            _log.Info($"CalculatePointDiscount cartPointPrice: {cartPointPrice} ");

            if (!string.IsNullOrEmpty(message))
                return 0;

            return (long)(point * pointPrice);
        }
        catch (Exception ex)
        {
            _log.Error($"Error in CalculatePointDiscount: {ex.Message}", ex);
        }

        return 0;
    }

    private TaxRateEnum GetTaxRateType(decimal taxRate)
    {
        return taxRate switch
        {
            0 => TaxRateEnum.Tax0,
            5 => TaxRateEnum.Tax5,
            8 => TaxRateEnum.Tax8,
            10 => TaxRateEnum.Tax10,
            _ => TaxRateEnum.Other
        };
    }

    private long GetTaxRateForItem(Shop shop, Items detailItem)
    {
        try
        {
            // Xác định thuế suất theo thứ tự ưu tiên:
            // 1. Nếu CustomTaxRate của Items = null -> lấy DefaultTaxRate của Shop
            // 2. Nếu CustomTaxRate = 0 hoặc DefaultTaxRate = 0 -> miễn thuế (trả về 0)
            if (!detailItem.CustomTaxRate.HasValue)
            {
                return (long)(shop?.DefaultTaxRate ?? 0);
            }

            if (detailItem.CustomTaxRate.Value == 0)
            {
                return 0;
            }

            return (long)detailItem.CustomTaxRate.Value;
        }
        catch (Exception ex)
        {
            _log.Error($"Error in GetTaxRateForItem: {ex.Message}", ex);
        }

        return 0;
    }
}