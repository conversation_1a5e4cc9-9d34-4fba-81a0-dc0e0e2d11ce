using System.ComponentModel.DataAnnotations;

namespace App.ECommerce.Resource.Dtos.GamificationDtos;

public class GameCampaignDto
{
    public string? Id { get; set; }

    [Required]
    public string ShopId { get; set; }

    [Required]
    public string Name { get; set; }

    [Required]
    public IFormFile Thumbnail { get; set; }

    public string? ThumbnailLink { get; set; }

    [Required]
    public string GameId { get; set; }
    public GameCampaignInfoDto? Game { get; set; }

    [Required]
    public DateTime StartTime { get; set; }

    [Required]
    public DateTime EndTime { get; set; }

    public Boolean? Active { get; set; }
}

public class GameCampaignInfoDto
{
    public string? Id { get; set; }
    public string? Name { get; set; }
}