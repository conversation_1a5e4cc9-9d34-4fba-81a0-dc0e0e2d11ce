﻿using App.Base.Repository;
using App.Base.Utilities;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units;

using log4net;

using MongoDB.Bson;
using MongoDB.Driver;

using Newtonsoft.Json;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

namespace App.ECommerce.Repository.Implement;

public class AffiliationPartnerRepository : BaseRepository, IAffiliationPartnerRepository
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(AffiliationPartnerRepository));
    private readonly IUserRepository _userRepository;
    private readonly ICommissionsConfigHelpers _commissionsConfigHelpers;
    private readonly IOrderRepository _orderRepository;
    private readonly ICommissionsReportRepository _commissionsReportRepository;
    private readonly IMembershipLevelRepository _membershipLevelRepository;
    private readonly ICommissionsCalculatorHeplers _commissionsCalculatorHeplers;
    private readonly ICommissionsConfigRepository _commissionsConfigRepository;

    public AffiliationPartnerRepository(IUserRepository userRepo, ICommissionsConfigHelpers commissionsConfigHelpers, IOrderRepository orderRepository, ICommissionsReportRepository commissionsReportRepository, IMembershipLevelRepository membershipLevelRepository, ICommissionsCalculatorHeplers commissionsCalculatorHeplers, ICommissionsConfigRepository commissionsConfigRepository) : base()
    {
        _userRepository = userRepo;
        _commissionsConfigHelpers = commissionsConfigHelpers;
        _orderRepository = orderRepository;
        _commissionsReportRepository = commissionsReportRepository;
        _membershipLevelRepository = membershipLevelRepository;
        _commissionsCalculatorHeplers = commissionsCalculatorHeplers;
        _commissionsConfigRepository = commissionsConfigRepository;
    }

    public async Task<QuickReportDto?> GetQuickReport(string userId, string shopId)
    {
        var commissionConfig = await _commissionsConfigHelpers.GetCommissonConfig(shopId);
        var networkUsers = await GetNetworkUsers(userId, commissionConfig);
        var buyerStats = await GetBuyerStats(userId, networkUsers.f1UserIds, networkUsers.f2UserIds);
        var user = _userRepository.FindByUserId(userId);
        var approvalDate = user.ApprovalDate ?? DateTime.MinValue;

        var f0Stats = await GetF0Stats(userId, commissionConfig, approvalDate);
        var f1Stats = await GetF1Stats(networkUsers.f1UserIds, userId, commissionConfig, approvalDate);
        var f2Stats = await GetF2Stats(networkUsers.f2UserIds, userId, commissionConfig, approvalDate);

        return new QuickReportDto
        {
            TotalCustomers = networkUsers.totalF1Count + networkUsers.totalF2Count + 1,
            CustomersPurchased = buyerStats.totalF1Buyers + buyerStats.totalF2Buyers + buyerStats.totalF0Buyer,
            SuccessfulOrders = f0Stats.successCount + f1Stats.successCount + f2Stats.successCount,
            TotalRevenue = f0Stats.revenue,
            PendingCommission = f0Stats.pendingCommission,
            PaidCommission = f0Stats.paidCommission
        };
    }

    private async Task<(decimal revenue, int successCount, decimal pendingCommission, decimal paidCommission)> GetF0Stats(string userId, CommissionsConfig commissionConfig, DateTime start)
    {
        return await GetUserStats(new List<string> { userId }, userId, start, CommissionLevel.F0);
    }

    private async Task<(decimal revenue, int successCount, decimal pendingCommission, decimal paidCommission)> GetF1Stats(List<string> f1UserIds, string userId, CommissionsConfig commissionConfig, DateTime start)
    {
        return f1UserIds.Any()
            ? await GetUserStats(f1UserIds, userId, start, CommissionLevel.F1)
            : (0, 0, 0, 0);
    }

    private async Task<(decimal revenue, int successCount, decimal pendingCommission, decimal paidCommission)> GetF2Stats(List<string> f2UserIds, string userId, CommissionsConfig commissionConfig, DateTime start)
    {
        return f2UserIds.Any()
            ? await GetUserStats(f2UserIds, userId, start, CommissionLevel.F2)
            : (0, 0, 0, 0);
    }

    private async Task<(List<string> f1UserIds, List<string> f2UserIds, int totalF1Count, int totalF2Count)> GetNetworkUsers(string userId, CommissionsConfig commissionConfig)
    {
        var f1Users = await _userRepository.GetF1Users(userId);

        var f2Users = await _userRepository.GetF2Users(f1Users.f1UserIds);

        return (f1Users.f1UserIds, f2Users.f2UserIds, f1Users.f1Count, f2Users.f2Count);

    }

    private async Task<(decimal revenue, int successCount, decimal pendingCommission, decimal paidCommission)> GetUserStats(IEnumerable<string> userIds, string targetUserId, DateTime start,
        CommissionLevel commissionLevel)
    {
        decimal pendingCommission = 0;
        decimal paidCommission = 0;
        decimal revenue = 0;
        int successCount = 0;
        if (commissionLevel == CommissionLevel.F0)
        {
            var userF0filter = Builders<Order>.Filter.And(Builders<Order>.Filter.ElemMatch(order => order.CommissionBreakUp.CommissionDistribution,
                                            Builders<CommissionDistribution>.Filter.And(
                                                Builders<CommissionDistribution>.Filter.In(cd => cd.UserId, userIds),
                                                Builders<CommissionDistribution>.Filter.Eq(cd => cd.IsActive, true)
                                            )));

            var successOrdersFilter = userF0filter
                         & Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success)
                         & Builders<Order>.Filter.Gte(o => o.Created, start);
            var successOrders = await _orderRepository.GetOrdersByFilter(successOrdersFilter);
            successCount = successOrders.Count();

            var successCommissionOrdersFilter = userF0filter
                          & Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success)
                          & Builders<Order>.Filter.Gte(o => o.Created, start);
            var successCommissionOrders = await _orderRepository.GetOrdersByFilter(successCommissionOrdersFilter);

            var revenueStatusFilter = userF0filter
                                        & Builders<Order>.Filter.In(o => o.StatusOrder, new[] { TypeOrderStatus.Success, TypeOrderStatus.Pending });
            var revenueOrders = await _orderRepository.GetOrdersByFilter(revenueStatusFilter);

            var pendingFilter = userF0filter
                               & Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Pending)
                               & Builders<Order>.Filter.Gte(o => o.Created, start);

            var pendingOrders = await _orderRepository.GetOrdersByFilter(pendingFilter);

            revenue = _commissionsCalculatorHeplers.CalculateCommissionAndRevenue(revenueOrders, userIds.FirstOrDefault()).revenue;
            pendingCommission = _commissionsCalculatorHeplers.CalculateCommissionAndRevenue(pendingOrders, userIds.FirstOrDefault()).commission;
            paidCommission = _commissionsCalculatorHeplers.CalculateCommissionAndRevenue(successCommissionOrders, userIds.FirstOrDefault()).commission;
        }
        return (revenue, successCount, pendingCommission, paidCommission);
    }
    private async Task<(int totalF1Buyers, int totalF2Buyers, int totalF0Buyer)> GetBuyerStats(string f0UserId, List<string> f1UserIds, List<string> f2UserIds)
    {
        var successOrders = await _orderRepository.GetOrdersByFilter(
            Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success));

        int CountBuyers(IEnumerable<string> userIds) =>
            successOrders?
                .Where(o => userIds?.Contains(o.Creator?.UserId) == true)
                .Select(o => o.Creator?.UserId)
                .Distinct()
                .Count() ?? 0;

        return (CountBuyers(f1UserIds), CountBuyers(f2UserIds), CountBuyers(new[] { f0UserId }));
    }

    public async Task<bool> CanUserAffordPurchaseAsync(string userId, string shopId)
    {
        var commissionConfig = await _commissionsConfigHelpers.GetCommissonConfig(shopId);

        var ordersFilter = Builders<Order>.Filter.Eq(o => o.Creator.UserId, userId)
                              & Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success);
        var orders = await _orderRepository.GetOrdersByFilter(ordersFilter);

        // if (!orders.Any()) return false;

        var revenue = orders.Sum(o => o.Price - o.TransportPrice - o.VoucherPromotionPrice);

        var requiredRevenue = commissionConfig.AdvancedCommissionsConfig.MinSpendToApproved;
        if (requiredRevenue == null) return true;

        return revenue >= requiredRevenue;
    }

    private (int point, long? minPurchase) CalcPointEarnByShare(string shopId)
    {
        // Phân tích cú pháp chuỗi JSON EarnPoint
        var configMembershipLevel = _membershipLevelRepository.GetConfigMembership(shopId);
        if (configMembershipLevel == null) return (0, null);

        if (!configMembershipLevel.Status) return (0, null);

        var earnPointDto = JsonConvert.DeserializeObject<EarnPointDto>(configMembershipLevel.EarnPoint);
        if (!earnPointDto.ShareJson.Status) return (0, null);
        var newPoint = earnPointDto.ShareJson.Rule;


        //Validate có yêu cầu mua hàng không
        var isRequirePurchase = earnPointDto.ShareJson.IsPurchase.isRequire;
        if (isRequirePurchase)
        {
            long requireMinSpent = earnPointDto.ShareJson.IsPurchase.minSpent;
            return (newPoint, requireMinSpent);

        }

        return (newPoint, null);

    }

    public async Task<List<User>> GetUsersWithSuccessfulOrders(List<string> userIds)
    {
        // Lấy danh sách người dùng theo userIds
        var users = await _userRepository.ListUserByUserIds(userIds);

        // Lấy danh sách đơn hàng thành công của những người dùng này
        var orders = await _orderRepository.GetOrdersByFilter(Builders<Order>.Filter.And(
            Builders<Order>.Filter.In(o => o.Creator.UserId, userIds),
            Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success)
        ));

        // Lấy danh sách UserId của những người dùng đã có đơn hàng thành công
        var usersWithSuccessfulOrders = orders.Select(o => o.Creator.UserId).Distinct().ToList();

        // Lọc ra những người dùng có đơn hàng thành công
        var usersWithOrders = users.Where(u => usersWithSuccessfulOrders.Contains(u.UserId)).ToList();

        return usersWithOrders;
    }

    public async Task<List<User>> GetUsersWithoutOrders(List<string> userIds)
    {
        // Lấy danh sách người dùng theo userIds
        var users = await _userRepository.ListUserByUserIds(userIds);

        // Lấy danh sách đơn hàng thành công của những người dùng này
        var orders = await _orderRepository.GetOrdersByFilter(Builders<Order>.Filter.And(
            Builders<Order>.Filter.In(o => o.Creator.UserId, userIds),
            Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success)
        ));

        // Lấy danh sách UserId của những người dùng đã có đơn hàng
        var usersWithOrders = orders.Select(o => o.Creator.UserId).Distinct().ToList();

        // Lọc ra những người dùng chưa có đơn hàng
        var usersWithoutOrders = users.Where(u => !usersWithOrders.Contains(u.UserId)).ToList();

        return usersWithoutOrders;
    }


    private (DateTime startDate, DateTime endDate) GetCommissionDateRange(string shopId, CommissionsConfig commissionConfig)
    {
        var today = DateTime.Today;
        var isBeforePaymentDue = today.Day <= commissionConfig.AdvancedCommissionsConfig.PaymentDue;

        var startDate = isBeforePaymentDue
            ? new DateTime(today.Year, today.Month, 1).AddMonths(-1)
            : new DateTime(today.Year, today.Month, 1);

        return (startDate, today);
    }



    public async Task<UserAffiliateReportDto?> GetUserAffiliateReport(string userId, string shopId)
    {
        var commissionConfig = await _commissionsConfigRepository.FindCommissionsConfig(shopId);

        var (pointEarnByShare, requireMinSpent) = CalcPointEarnByShare(shopId);

        var user = _userRepository.FindByUserId(userId);
        var approvalDate = user.ApprovalDate ?? DateTime.MinValue;

        // var dateRange = GetCommissionDateRange(shopId, commissionConfig);
        var networkUsers = await GetNetworkUsers(userId, commissionConfig);
        var f0Stats = await GetF0Stats(userId, commissionConfig, approvalDate);
        var f1Stats = await GetF1Stats(networkUsers.f1UserIds, userId, commissionConfig, approvalDate);
        var f2Stats = await GetF2Stats(networkUsers.f2UserIds, userId, commissionConfig, approvalDate);
        var allChildrenUserIds = networkUsers.f1UserIds.Concat(networkUsers.f2UserIds).ToList();
        allChildrenUserIds.Add(userId); // Thêm user.UserId vào danh sách
        var userIdsExcludingSelf = allChildrenUserIds.Where(id => id != userId).ToList();

        // Xác định startDate và endDate
        var today = DateTime.Today;
        var startDate = new DateTime(today.Year, today.Month, 1); // Ngày đầu tháng hiện tại
        var endDate = today.AddDays(1).AddTicks(-1); // Hết ngày hôm nay


        decimal f0ListCommission = 0;

        var f0Commission = await GetUserCommissionList(userId, 0, commissionConfig, TypeCommission.Approved, approvalDate, startDate, endDate);
        f0ListCommission = f0Commission.Sum(c => c.CommissionPrice); // Tính tổng hoa hồng cho f0


        var thisMonthApprovedCommission = f0ListCommission; // Cộng thêm f0ListCommission


        var totalUserWithPurchased = (await GetUsersWithSuccessfulOrders(allChildrenUserIds)).Count;
        var totalUserWithoutPurchased = (await GetUsersWithoutOrders(userIdsExcludingSelf)).Count;
        var totalCommissionReceived = await _commissionsReportRepository.TotalCommissionValue(userId);

        return new UserAffiliateReportDto
        {
            TotalCustomers = 1 + networkUsers.totalF1Count + networkUsers.totalF2Count,
            SuccessfulOrders = f0Stats.successCount + f1Stats.successCount + f2Stats.successCount,
            //hh chờ duyệt
            PendingCommission = (long)Math.Round(f0Stats.pendingCommission + f1Stats.pendingCommission + f2Stats.pendingCommission),
            //hh đã duyệt
            PaidCommission = (long)Math.Round(f0Stats.paidCommission + f1Stats.paidCommission + f2Stats.paidCommission),
            RequireMinSpent = requireMinSpent,
            //điểm nhận được khi chia sẻ thành công
            PointEarnByShare = pointEarnByShare,
            TotalUserWithPurchased = totalUserWithPurchased,
            TotalUserWithoutPurchase = totalUserWithoutPurchased,
            LevelOneCommissionPercentage = commissionConfig.BasicCommissionsConfig.LevelOneCommissionPercentage,
            LevelTwoCommissionPercentage = commissionConfig.BasicCommissionsConfig.LevelTwoCommissionPercentage,
            //hh tháng này
            ThisMonthApprovedCommission = thisMonthApprovedCommission,
            TotalCommissionReceived = totalCommissionReceived

        };
    }

    public async Task<List<MyTeamResultDto>> GetMyTeam(User user, string? search, TypeMyTeam? type = null)
    {
        var commissionConfig = await _commissionsConfigRepository.FindCommissionsConfig(user.ShopId);

        var networkUsers = await GetNetworkUsers(user.UserId, commissionConfig);
        var allChildrenUserIds = networkUsers.f1UserIds.Concat(networkUsers.f2UserIds).ToList();
        allChildrenUserIds.Add(user.UserId); // Thêm user.UserId vào danh sách
        var userIdsExcludingSelf = allChildrenUserIds.Where(id => id != user.UserId).ToList();
        // Tạo bộ lọc cho người dùng
        var filter = Builders<User>.Filter.In(u => u.UserId, allChildrenUserIds);

        // Kiểm tra type
        if (type == TypeMyTeam.Purchased)
        {
            var usersWithSuccessfulOrders = await GetUsersWithSuccessfulOrders(allChildrenUserIds);
            filter = Builders<User>.Filter.In(u => u.UserId, usersWithSuccessfulOrders.Select(u => u.UserId));
        }
        else if (type == TypeMyTeam.NotPurchase)
        {
            var usersWithoutOrders = await GetUsersWithoutOrders(userIdsExcludingSelf);
            filter = Builders<User>.Filter.In(u => u.UserId, usersWithoutOrders.Select(u => u.UserId));
        }

        // Nếu có search, thêm điều kiện tìm kiếm vào bộ lọc
        if (!string.IsNullOrWhiteSpace(search))
        {
            var searchFilter = Builders<User>.Filter.Or(
                Builders<User>.Filter.Regex(x => x.Fullname, new BsonRegularExpression($@"{search}".EscapeSpecialChars(), "i")),
                Builders<User>.Filter.Regex(x => x.PhoneNumber, new BsonRegularExpression($@"{search}".EscapeSpecialChars(), "i"))
            );
            filter = Builders<User>.Filter.And(filter, searchFilter);
        }

        // Lấy danh sách người dùng theo bộ lọc
        var users = await _userRepository.GetUsersByFilter(filter);
        // Tạo danh sách kết quả
        var result = new List<MyTeamResultDto>();

        // Nếu type là null, thêm tất cả người dùng vào danh sách kết quả
        foreach (var subUser in users)
        {
            var ordersFilter = Builders<Order>.Filter.And(
                Builders<Order>.Filter.Eq(o => o.Creator.UserId, subUser.UserId),
                Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success),
                Builders<Order>.Filter.ElemMatch(o => o.CommissionBreakUp.CommissionDistribution,
                    Builders<CommissionDistribution>.Filter.And(
                        Builders<CommissionDistribution>.Filter.Eq(cd => cd.UserId, user.UserId),
                        Builders<CommissionDistribution>.Filter.Eq(cd => cd.IsActive, true)
                    ))
            );
            var orders = await _orderRepository.GetOrdersByFilter(ordersFilter);
            var totalPrice = orders.Any() ? orders.Sum(o => o.Price) : 0;

            string fLevel = user.UserId == subUser.UserId ? "0" : (networkUsers.f1UserIds.Contains(subUser.UserId) ? "1" : "2");

            var newData = new MyTeamResultDto
            {
                UserId = subUser.UserId,
                FullName = subUser.Fullname,
                Avatar = subUser.Avatar,
                TotalPurchase = totalPrice,
                FLevel = fLevel,
                Created = subUser.Created
            };

            if (subUser.Avatar != null)
            {
                newData.Avatar = subUser.Avatar.IndexOfList(["https://", "http://"]) >= 0 ? subUser.Avatar : S3Upload.GetUrlImage(subUser.Avatar);
            }


            // Thêm vào danh sách kết quả
            result.Add(newData);
        }
        result = result.OrderBy(r => r.FLevel).ToList();
        return result;
    }

    private async Task<List<MyCommissionResultDto>> GetUserCommissionList(string f0UserId, int level, CommissionsConfig commissionsConfig, TypeCommission typeCommission, DateTime? approvalDate, DateTime? startDate = null, DateTime? endDate = null)
    {
        var orderStatus = new List<TypeOrderStatus>() { TypeOrderStatus.Success };
        if (typeCommission == TypeCommission.Pending)
        {
            orderStatus = new List<TypeOrderStatus>() { TypeOrderStatus.Pending };
        }

        var ordersFilter = Builders<Order>.Filter.And(Builders<Order>.Filter.ElemMatch(order => order.CommissionBreakUp.CommissionDistribution,
        Builders<CommissionDistribution>.Filter.And(
                                            Builders<CommissionDistribution>.Filter.Eq(cd => cd.UserId, f0UserId),
                                             Builders<CommissionDistribution>.Filter.Eq(cd => cd.IsActive, true)
                                             )))
                          & Builders<Order>.Filter.In(o => o.StatusOrder, orderStatus)
                          & Builders<Order>.Filter.Gte(o => o.Created, approvalDate);
        // Thêm điều kiện lọc theo startDate và endDate nếu có
        if (startDate.HasValue)
        {
            ordersFilter = Builders<Order>.Filter.And(ordersFilter, Builders<Order>.Filter.Gte(o => o.Created, startDate.Value));
        }
        if (endDate.HasValue)
        {
            ordersFilter = Builders<Order>.Filter.And(ordersFilter, Builders<Order>.Filter.Lte(o => o.Created, endDate.Value));
        }
        var orders = await _orderRepository.GetOrdersByFilter(ordersFilter);
        var result = new List<MyCommissionResultDto>();
        foreach (var order in orders)
        {
            // Bỏ qua đơn hàng nếu Creator hoặc UserId bị null/rỗng
            if (order.Creator == null || string.IsNullOrEmpty(order.Creator.UserId))
            {
                continue;
            }

            var userOrder = _userRepository.FindByUserId(order.Creator.UserId);

            // Bỏ qua đơn hàng nếu không tìm thấy user
            if (userOrder == null)
            {
                continue;
            }

            CommissionLevel levelName = level == 0 ? CommissionLevel.F0 :
                            level == 1 ? CommissionLevel.F1 :
                                         CommissionLevel.F2;
            long commissionPrice = 0;

            if (_commissionsCalculatorHeplers != null)
            {
                var calculationResult = _commissionsCalculatorHeplers.CalculateCommissionAndRevenue(new List<Order> { order }, f0UserId);
                commissionPrice = (long)Math.Round(calculationResult.commission, MidpointRounding.AwayFromZero);
            }

            var commissionResult = new MyCommissionResultDto
            {
                UserId = order.Creator.UserId,
                FullName = userOrder.Fullname,
                OrderId = order.OrderId,
                CommissionPrice = commissionPrice,
                Avatar = userOrder.Avatar,
                Created = order.Created,
                OrderNo = order.OrderNo
            };
            if (userOrder.Avatar != null)
            {
                commissionResult.Avatar = userOrder.Avatar.IndexOfList(["https://", "http://"]) >= 0 ? userOrder.Avatar : S3Upload.GetUrlImage(userOrder.Avatar);
            }
            result.Add(commissionResult);
        }

        return result;

    }

    public async Task<List<MyCommissionResultDto>> GetUserListCommission(User user, TypeCommission type = TypeCommission.Approved)
    {
        var commissionConfig = await _commissionsConfigRepository.FindCommissionsConfig(user.ShopId);
        List<MyCommissionResultDto> f0Commission = new List<MyCommissionResultDto>();
        var approvalDate = user.ApprovalDate;


        f0Commission = await GetUserCommissionList(user.UserId, 0, commissionConfig, type, approvalDate);

        var listCommission = f0Commission
                                     .OrderByDescending(commission => commission.Created) // Sắp xếp theo Created giảm dần
                                     .ToList();
        return listCommission;
    }

    public async Task<(List<string> f1UserIds, List<string> f2UserIds)> ListSubUserIds(User user)
    {
        var commissionConfig = await _commissionsConfigRepository.FindCommissionsConfig(user.ShopId);

        var networkUsers = await GetNetworkUsers(user.UserId, commissionConfig);
        return (networkUsers.f1UserIds, networkUsers.f2UserIds);

    }

    public async Task UpdateUserAffiliationStatus(string userId)
    {
        var user = _userRepository.FindByUserId(userId);
        if (user == null) return;
        //user chưa đăng ký thành đối tác thì skip
        if (user.AffiliationStatus != AffiliationTypeStatus.InActived) return;

        var commissionsConfig = await _commissionsConfigHelpers.GetCommissonConfig(user.ShopId);

        if (commissionsConfig?.AdvancedCommissionsConfig?.IsAutoApproved == true)
        {
            var canAfford = await CanUserAffordPurchaseAsync(user.UserId, user.ShopId);
            if (canAfford)
            {
                user.AffiliationStatus = AffiliationTypeStatus.Actived;
                user.ApprovalDate = DateTime.Now;
                user.AffiliationExpireDate = _commissionsConfigHelpers.CalculateExpirationDate(commissionsConfig?.AdvancedCommissionsConfig?.PartnerCommExpiry ?? 1, user.ApprovalDate ?? DateTime.Now);
                _userRepository.UpdateUser(user);
            }
        }

        // Thêm logic cập nhật trạng thái ở đây
    }

    public bool HasReferralCycle(string userId, string? parentId)
    {
        try
        {
            var currentParentId = parentId;

            // Traverse ngược lên theo từng ParentId
            while (!string.IsNullOrEmpty(currentParentId))
            {
                if (currentParentId == userId)
                {
                    // Nếu gặp lại user ban đầu ➔ vòng lặp
                    return true;
                }

                var parentUser = _userRepository.FindByUserId(currentParentId);
                if (parentUser == null)
                {
                    // Nếu không tìm thấy parent, kết thúc
                    break;
                }

                currentParentId = parentUser.ParentId;
            }
        }
        catch (Exception ex)
        {
            _log4net.Error(ex);
        }

        return false;
    }
    private async Task<(List<string> f1UserIds, int totalF1Count, List<string> f2UserIds, int totalF2Count)> GetF1AndF2Users(string f0UserId)
    {

        var filter = Builders<Order>.Filter.And(Builders<Order>.Filter.ElemMatch(order => order.CommissionBreakUp.CommissionDistribution,
                                             Builders<CommissionDistribution>.Filter.Eq(cd => cd.UserId, f0UserId)));
        var orders = await _orderRepository.GetOrdersByFilter(filter);

        var f1UserIds = orders
            .Where(o => o.CommissionBreakUp.CommissionDistribution.Any(cd => cd.UserId == f0UserId && cd.CommissionLevel == CommissionLevel.F1))
            .Select(o => o.Creator?.UserId)
            .Where(id => !string.IsNullOrEmpty(id))
            .Distinct()
            .ToList();

        var f2UserIds = orders
            .Where(o => o.CommissionBreakUp.CommissionDistribution.Any(cd => cd.UserId == f0UserId && cd.CommissionLevel == CommissionLevel.F2))
            .Select(o => o.Creator?.UserId)
            .Where(id => !string.IsNullOrEmpty(id))
            .Distinct()
            .ToList();

        return (f1UserIds, f1UserIds.Count, f2UserIds, f2UserIds.Count);
    }
}




